{"name": "ai-gatwway", "version": "0.0.1", "description": "React With BFF", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install": "npm run setup", "start": "concurrently \"npm run start:client\" \"npm run start:server\" ", "start:server": "cd src/server && npm start", "start:client": "cd src/client && npm start", "setup": "concurrently \"npm run install:client\"  \"npm run install:server\" ", "install:client": "cd src/client && npm install", "install:server": "cd src/server && npm install", "build": "concurrently \"npm run build:client\" \"npm run build:server\" ", "build:client": "cd src/client && npm run build", "build:server": "cd src/server && npm run build"}, "author": "<PERSON>", "license": "ISC", "devDependencies": {"lerna": "^6.0.3", "npm-watch": "^0.11.0", "simple-copy": "^2.2.1"}, "dependencies": {"concurrently": "^7.6.0"}}