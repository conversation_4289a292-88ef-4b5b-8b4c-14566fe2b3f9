## [1.13.2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.13.1...1.13.2) (2025-05-26)


### Bug Fixes

* centralize processingStartDate setting in perFormTask method ([7a25fca](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7a25fca662474d75781f6adcb7e5739b9788781a))
* ensure tasks are always created with IN_QUEUE status ([1e01a41](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1e01a415daaf4027cc3b0c9cc53e07a47ab75eb7))
* only get a count instead of retrieving full task objects ([eae0b4d](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/eae0b4d0409645329e2e1e2e69dfcddaeb1aba8c))
* optimize queue management ([d4642aa](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/d4642aa6c0f2935a3ed82125d90b275ad3796915))
* prevent concurrent task processing for services with active tasks ([9c6865e](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/9c6865ef077730edbd1a54b769edfc3fd35abe3c))
* remove creation of analysisresults ([52dc2b5](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/52dc2b59fc78b8540926c5435c974188faccee9d))
* return consistent error responses instead of rejecting promises ([b5fe894](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b5fe8944bf118962d9203f5a50b19ba7e381706e))
* revert api call optimizations ([18acad9](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/18acad90d3b7d9bd504112dc2cfcfe3f6becfc6b))

## [1.13.1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.13.0...1.13.1) (2025-05-16)


### Bug Fixes

* Only update task if request to service is successful ([a1b1115](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/a1b1115e5958a745e31bfb81a0b287b3ff608b6a))

# [1.13.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.12.2...1.13.0) (2025-05-14)


### Bug Fixes

* include dependency ([bc44d7a](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/bc44d7a99c54d388d85a9f5495c07cb1658db682))
* remove stale task timeout handling logic ([ed43e55](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/ed43e55a6c4986ab4c2e77358d2bf37ff3750878))
* rename startupFunction file and fix import paths ([db33f13](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/db33f1332a7e1bf775b813a9bc99614bfcb714af))
* startupFunction file typo ([ded16b7](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/ded16b7ce13e3fdd4752eb0c7f88432a30453f28))
* use singleton pattern for azure service bus ([bff5609](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/bff5609b0293ad55120429bafd65e8611a6f879c))


### Features

* ensure docserverId exists before creating/updating task ([b97be3d](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b97be3de5cf7d198bb5d237085229a9a41a6f487))
* improve logging for background task processor ([bb058ee](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/bb058eec0e58f44d502bb1c5f2f36e1f6c801c2e))
* improved logging visibility ([b48ed4a](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b48ed4a139916e954049892fa83b5bd9d8d1a791))
* move docserver interactions to background tasks ([28de343](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/28de343570c92702016b38ba78e57ad988a4a458))
* remove performTask from createTask and updateTask ([a50a1d8](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/a50a1d862c4684c5b6ab8cceff29f8e7b918aade))
* simplify priority adjustment logic for existing tasks ([5ba75b0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/5ba75b024c036019c2884d9bd9241b428141f3d2))
* update logging for readability ([67577b2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/67577b2b4e70d53cd3f0fa3071df441d81ce758a))
* update schedule to run every 10s instead of 10m ([7e42f83](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7e42f836cdaa3da4b5cb9d1a8174048507c1a604))
* use processingStartDate instead of createdDate for task timeout ([a0002c6](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/a0002c66e28558cf5ba9128e28db0f19c5d4fa1a))

## [1.11.2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.11.1...1.11.2) (2025-04-10)


### Bug Fixes

* Updated README.md ([be2c287](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/be2c287ef01933cc162cac9203de257a06171c61))

## [1.11.1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.11.0...1.11.1) (2025-04-10)


### Bug Fixes

* reset all in progress task to in_queue. always kick start the first task for all services. ([4a25a58](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/4a25a5827419a31b9e9c0fcaaaf0deddbc4e2cc7))

# [1.11.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.10.0...1.11.0) (2025-02-20)


### Bug Fixes

* increaase retry delay and max retries ([fe1db24](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/fe1db247a93f8581edebb69a1ed488ee3bc0c890))
* increasae retry delay ([1bd7aaf](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1bd7aaf351a326b053192ce9bebf3b1c22302537))
* increase request size ([886c728](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/886c7281be41a9720688f73eece714e4bf7aefef))
* input log on instantiation ([f06a80b](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f06a80b58cad38b77cb6b544dc8574036e1c5f8e))
* use sb connection string ([32e683c](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/32e683c885feb909e3a97fad27c5dc0b4d408161))


### Features

* [#15443](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/15443) websocket connection for service bus ([7cc63d2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7cc63d217951a002e1878f3f9681d5ccf1def086))

# [1.10.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.9.1...1.10.0) (2025-01-10)


### Bug Fixes

* change type to boolean ([3715b3a](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3715b3a8e04c77005200e437ae94259eca9fe7fe))
* find by task id ([33e1f40](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/33e1f40d219ff89060b9516ab6c55b85bb17e923))
* save fileid ([8393fcf](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/8393fcff69808df20212498d69fd7488ce4eab80))
* update message bus status on mongo ([29ce798](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/29ce79826d56173a13881bb0927433f6a13eed98))
* update mongoose types for new flags ([82529a2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/82529a298c29514cd374aa5ef934e467c38b7428))


### Features

* retry mechanism for send message bus ([15d1f53](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/15d1f53362eaae173b0bcffa702e487d1f5036b0))
* tags for receiving data and sending message ([4a6a281](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/4a6a2810685d682ad4f9632fdda248020048a24c))

## [1.9.1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.9.0...1.9.1) (2025-01-03)


### Bug Fixes

* change retreivla of tasks to lifo ([07954b1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/07954b1346fe4d63802002fb090f76f6364256d6))
* dummy commit ([ebf4ea8](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/ebf4ea8b162a6c9c5a827d0d835f66dbf3f46792))
* logs for messagebus ([2b9f779](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/2b9f779dc041a5f04a5850e0485cdb8a6dfc3bcb))

# [1.9.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.8.0...1.9.0) (2024-09-23)


### Bug Fixes

* dummy commit ([737e030](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/737e030b57f1bb8c9e43c564da019549d7204bf8))


### Features

* env var kill time ([f68c361](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f68c3616e644cc614f60248de2108ac61726aa34))

# [1.8.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.7.3...1.8.0) (2024-09-09)


### Bug Fixes

* just added some logs ([6bba5be](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/6bba5bebffe1db366b59d9df31edede0f2b3911d))
* remove quota api ([d78b91e](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/d78b91e5284603927061c2a15f82d35547999bf4))
* updated the cybersmart react package ([1b58c32](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1b58c3299f31c459cc1891ba20123427fe810d74))


### Features

* [#12107](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/12107) send priority ([a15204b](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/a15204b0c395ca58caefe1479bc18494903985b9))

## [1.7.3](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.7.2...1.7.3) (2024-04-17)


### Bug Fixes

* resolve startup issue on cce ([9f21b07](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/9f21b074b1f72a8b42f8d9e4676d85873326b2ec))

## [1.7.2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.7.1...1.7.2) (2024-04-17)


### Bug Fixes

* call quota api in dev only ([7b40a39](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7b40a39a8ab856480d9787582d2ab62929eb3077))

## [1.7.1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.7.0...1.7.1) (2024-04-17)


### Bug Fixes

* dummy commit Updated README.md ([f5a45af](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f5a45afd8f347eb2cea60dc0e7413f51baf569d2))

# [1.7.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.6.0...1.7.0) (2024-03-21)


### Features

* [#6520](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6520) implement rate quota api ([adffe69](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/adffe69246053b47b5dea3b0235421bf0f4d2b85))

# [1.6.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.5.0...1.6.0) (2024-03-12)


### Bug Fixes

* [#6337](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6337) get analysed data by taskid ([e148e0a](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/e148e0a185f8f415a016f9413c7290e634d11b21))
* metadata saved location ([1585dfb](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1585dfbd2b10c1174087f83fb5d80e60719be1a4))


### Features

* [#6228](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6228) env query param ([7d64083](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7d64083b4d651a8f51c422eccb7fa46a855d68e9))
* [#6334](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6334) include metadata option ([0bd5113](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/0bd51137284a4db628683bf79ab55a20681010d5))
* added descriptions for api ([eb13184](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/eb13184193bcaf4995aa5e138a0701680ce97e9f))

# [1.5.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.4.0...1.5.0) (2024-02-27)


### Features

* [#6070](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6070) [#6073](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/6073) queue status and statitics endpoints ([536275d](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/536275da8358888e6b179e7473d1408dd11526eb))

# [1.4.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.3.3...1.4.0) (2024-02-22)


### Features

* [#5985](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/5985) startup function to create collection ([f507427](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f50742757960b4071f95aa6af1d4a41ac39568d1))

## [1.3.3](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.3.2...1.3.3) (2024-02-21)


### Bug Fixes

* revieve mediatype from request body ([fb02812](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/fb0281254e05ba228d503dee1f66fa3c7ae74fef))

## [1.3.2](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.3.1...1.3.2) (2024-02-21)


### Bug Fixes

* [#5924](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/5924) swagger details ([043484b](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/043484b15b0e460d8115d186b4b131a2622eb5fe))

## [1.3.1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.3.0...1.3.1) (2024-02-20)


### Bug Fixes

* temp change to test message bus ([04a4660](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/04a4660a84769836de1d4c72702bbf563d154e3a))

# [1.3.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.2.0...1.3.0) (2024-02-20)


### Bug Fixes

* change create endpoint to pick up env var [#5708](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/5708) ([0c8145f](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/0c8145f213d81354802248f16a2ec7e6e71a34f9))
* import issues ([1cb2712](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1cb2712b9cb64436c63123f55a6b7c6165909159))
* placement of object params ([553c302](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/553c30247966cd6887403457883dd5a40e429926))
* registryUrl remove ui ([3c07e75](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3c07e751f8d34d4bfddffb45b928302411fc7083))
* remove docserver file collection update and vdb insertion ([279eedf](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/279eedf6d8d226da70852160127be20bb8206c63))
* send aiclientobject ([2096977](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/2096977343be73d3560b043edd9cb1c289d683e2))
* service bus endppt ([288fa30](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/288fa3029cad8cf8cfeea636e46bbf486d357fd8))
* serviceName ([f703ac5](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f703ac5a7ebcbac238e24484007d9eee20877f94))


### Features

* [#5770](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/5770) cronjob and startup function monitioring ([65628a8](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/65628a8a71808f41af50e6538d70c3b45e06d018))
* [#5862](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/issues/5862) cron job checks media type to decide max task processing time ([f54e9a5](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f54e9a506956d5f90e7020bed036869cddf23c3c))
* commented out processor duration check ([2074fb7](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/2074fb71dfb5a5489f99c2ef548dc65c6df75f41))
* create analysis env ([d207c03](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/d207c03d0d964e23ea73bf1dfd529df9c03c94a4))
* endpoint to fetch analysis results data ([c25af16](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/c25af16f9d9f6ea4771667aa47518f95469a8c18))
* message bus implementation ([dfc82be](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/dfc82bef5365c40530d5c11cb9ce31f1ea0e458b))
* messagebus flag ([f77c31e](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f77c31e84627840e1c076f343b53b3b1a2e980f7))
* send data to ai_client_server ([7e9da07](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7e9da071b9718cbe69ea08294f0df020d34064f8))

# [1.2.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.1.0...1.2.0) (2024-02-07)


### Bug Fixes

* Add code to insert entities into VDB post request ([763b5c6](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/763b5c6031c14433063d3e27eeb7dc8fb6756234))
* comparison ([ff6f4a9](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/ff6f4a98b52c9fcd4099ed2c10fc3dcd9b57e958))
* upload error message under file processor status ([75f4a35](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/75f4a35297355846c81abd9dce738cf719df94d0))


### Features

* include crawler for FR ([7294658](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/7294658a5d060908b47a28590c3084643a99e9fb))
* redaction tag ([fd59f06](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/fd59f062c0a22a3d82e9dc05dfbfce58a1f5616c))

# [1.1.0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/compare/1.0.0...1.1.0) (2024-01-03)


### Bug Fixes

*  connection string name ([9a2023b](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/9a2023b633e34470fe9c7b35ad4930e8882e228b))
* apiurl var name ([e658fcc](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/e658fcc28078496a088f78d0506c9ad4995c28b2))
* azure pipeline ([1bee204](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/1bee204a36fb7866dd632531ee0ca489dbd5c258))
* bff endpoint error in mashroom ([34bba13](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/34bba13b0ea6973d429d343119951946a5817887))
* catch error ([d6e6865](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/d6e6865f70d1e466b6e8cf9f831353b7ae213e0e))
* console log response ([bafc7d0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/bafc7d055bab63b3f02f7a0d483a10a9ffdbdeb4))
* date.now ([b6c80bf](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b6c80bfd5795a930b8eac33a319f40492e266e18))
* docserver data saving ([24d4d7f](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/24d4d7f614ae2195066d99531019584a7fab27de))
* env vars ([a80988b](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/a80988bdc9797d4e59e0a6b3b33dfbe73c687c1c))
* env vars ([6f7d623](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/6f7d62383df2d44b59fde36b4f8fba8baf075e04))
* error handling and cronjob ([50c12e6](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/50c12e6815708e2af1244b34edac70fd5a5934ca))
* error results upload ([c404123](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/c4041238b7b4cf206bbe484c35d1b6b69c2cf622))
* include tags and processort status ([b71af69](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b71af69add3197896979f43684202f426f74f0de))
* manager service await ([64e412e](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/64e412e0c62b0a5353e75e778bb30fcf894618c6))
* mediatype promise ([076dbcf](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/076dbcf33b10c180432e733520df1a70c104774e))
* merge array boolean ([28e31ac](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/28e31acab4b36bd2e8dff352708ee6e8c7e77c2e))
* merge to true ([3d3b774](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3d3b7744e0fa8aa5d66202da26e1dd495377ba19))
* remove results obj ([3684e98](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3684e9824736d2274680fb90d6bb61147f117603))
* remove similarfaces tag ([2070170](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/20701701d622fda0f6497e5369c42f0ba59921d6))
* requesttype for files collection ([48d1438](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/48d14386df534a188bb7c533da206d2dd45fe406))
* return response ([382742f](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/382742fcac26864dd05b4a5d9b5649f142d07508))
* save individually ([af25540](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/af25540c82ee902325e1e41d9c89593991130e15))
* save result into file collection ([b7f7c4e](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/b7f7c4e92957cdf200910b84e9f3f995035c1962))
* server run error due to missing loopback-connector-rest ([48a10e1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/48a10e14e893fe1963c78e5bced25da487b975fb))
* tslint parser ([4d0a9f9](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/4d0a9f963595176b9a744c3f4a468ceea3b2f4db))


### Features

* add entityid and pass to fr ([38bac78](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/38bac781eb928c02101a49427b93cbfdf9af3416))
* change in request type ([662c7a0](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/662c7a07aaf1fcf13bbd34ad489ffc4105acac75))
* devcontainers ([4f0941f](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/4f0941f4a2cb3160603c2b63b8c7a16b2eb734bc))
* devspace ([2c70bb1](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/2c70bb1f686ea1f5648d97e622cccb4fbba7bfb4))
* error handling and cron job ([f9517fc](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f9517fc622e2896ea62aabf67c6bb20775b23466))
* hit api ([eee3a74](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/eee3a745ce7c7562c9e1136050dc2415bab220f8))
* include mashroom context provider ([3b0dd93](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3b0dd93708d1ad43984511b36a73eea903bb2d90))
* init repo ([f26627c](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/f26627c2a109584e5fc8b9ed77cfb60f8333ef96))
* mediatype ([98d618c](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/98d618c8742d161ee4e93610231f0f6ad9eb2e1f))
* new azure pipeline ([3c0ca39](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/3c0ca3999d97f07d706b8cdadfce78864cde9d91))
* support webpack Module Federation and Mashroom ([ace8053](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/ace8053cda8c6abdb9a286e4521b87d52d3a44c0))
* task created and completed date ([c8d1c3f](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/c8d1c3f9ef9396fd70007efb31561f73cdfd0225))
* task manager service ([9323c55](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/9323c552c7ab17075afd48c10aa2348083d35b8f))
* tm services ([d825f2c](https://dev.azure.com/predictintel/data-ai/_git/AI-Gateway-Service/commit/d825f2ccffff6111400b75697e52b0ce6dd06860))

# 1.0.0 (2022-11-27)


### Bug Fixes

* tsconfig.tsbuildinfo causing no update in server. ([0c9b61a](https://gitlab.com/cybersmart/ui/sample-react-bff/commit/0c9b61a661f2360e92f12d9f223f9034f6f5e01d))


### Features

* Add default gitlab-ci ([6a2aae3](https://gitlab.com/cybersmart/ui/sample-react-bff/commit/6a2aae3f2ef4ed6f47873f0a4b9d797cec94f6e6))
* Mashroom integration ([7965da3](https://gitlab.com/cybersmart/ui/sample-react-bff/commit/7965da36f7b5e75d64f4206dc4b27cb236d05a7b))
