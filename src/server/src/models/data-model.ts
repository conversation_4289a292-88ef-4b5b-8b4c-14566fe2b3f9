import mongoose, {Document, Schema} from 'mongoose';
import {InputParam, queryParams} from './inputApi.model';
import {QueryVars} from './taskManager.model';

export enum ParamTypes {
  GET = 'GET',
  POST = 'POST',
  PUT = 'PUT',
  DELETE = 'DELETE',
  PATCH = 'PATCH',
}

export enum MediaTypes {
  IMAGE = 'Image',
  VIDEO = 'Video',
  AUDIO = 'Audio',
  TEXT = 'Text',
}

export enum StatusTypes {
  IN_PROGRESS = 'In Progress',
  IN_QUEUE = 'In Queue',
  COMPLETED = 'Completed',
  ERROR = 'Error',
}

export interface ApiModelDTO extends Document {
  serviceName: string;
  apiUrl: string;
  tag: string;
  requestType: string;
  mediaType: string;
  queryParams: queryParams[];
  inputParams: InputParam[];
  description: string;
}

export interface TaskManagerModelDTO extends Document {
  docServerId: string;
  apiUrl: string;
  serviceName: string;
  status: string;
  responseFileUrl: string;
  apiTag: string;
  queryVars: QueryVars[];
  apiBody: string;
  priority: number;
  mediaType: string;
  createdDate: Date;
  processingStartDate: Date;
  completedDate: Date;
  entity_id: string;
  crawler: string;
  env: string;
  metaData: string;
  fileId: string;
  _messageSentToBus: boolean;
  _messageSentClientServer: boolean;
}

const QueryParamSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  {_id: false},
);

const InputParamSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    type: {
      type: String,
      required: true,
    },
    required: {
      type: Boolean,
      default: false,
    },
  },
  {_id: false},
);

const apiSchema = new mongoose.Schema<ApiModelDTO>({
  serviceName: {
    type: String,
    required: true,
  },
  mediaType: {
    type: String,
    required: true,
    enum: MediaTypes,
  },
  apiUrl: {
    type: String,
    required: true,
    unique: true,
  },
  tag: {
    type: String,
    required: true,
    unique: true,
  },
  requestType: {
    type: String,
    required: true,
    enum: ParamTypes,
  },
  queryParams: {
    type: [QueryParamSchema],
    default: void 0,
  },
  inputParams: {
    type: [InputParamSchema],
    default: void 0,
  },
  description: {
    type: String,
    required: true,
  },
});

const QueryVarSchema = new mongoose.Schema(
  {
    variable: {
      type: String,
      required: true,
    },
    value: {
      type: String,
      required: true,
    },
  },
  {_id: false},
);

const taskSchema = new mongoose.Schema<TaskManagerModelDTO>({
  docServerId: {
    type: String,
  },
  serviceName: {
    type: String,
    required: true,
  },
  mediaType: {
    type: String,
    required: true,
    enum: MediaTypes,
  },
  apiUrl: {
    type: String,
    required: true,
  },
  responseFileUrl: {
    type: String,
  },
  apiTag: {
    type: String,
    required: true,
  },
  apiBody: {
    type: String,
  },
  priority: {
    type: Number,
    required: true,
  },
  queryVars: {
    type: [QueryVarSchema],
    default: void 0,
  },
  status: {
    type: String,
    required: true,
    enum: Object.values(StatusTypes),
    default: StatusTypes.IN_QUEUE,
  },
  env: {
    type: String,
  },
  createdDate: {
    type: Date,
    default: Date.now,
  },
  processingStartDate: {
    type: Date,
  },
  completedDate: {
    type: Date,
  },
  entity_id: {
    type: String,
  },
  crawler: {
    type: String,
  },
  metaData: {
    type: String,
  },
  fileId: {
    type: String,
  },
  _messageSentToBus: {
    type: Boolean,
  },
  _messageSentClientServer: {
    type: Boolean,
  },
});

const apiServiceSchema = new mongoose.Schema(
  {
    openapi: String,
    info: {
      title: {type: String, required: true},
      version: {type: String, required: true},
    },
    paths: mongoose.Schema.Types.Mixed, // or {} for a mixed type
    servers: [
      new mongoose.Schema(
        {
          url: String,
        },
        {_id: false},
      ),
    ],
    components: mongoose.Schema.Types.Mixed,
  },
  {minimize: false},
);

apiServiceSchema.index({'info.title': 1, 'info.version': 1}, {unique: true});

export const swaggerApiData = mongoose.model(
  'apiServiceSchema',
  apiServiceSchema,
);

export const apiData = mongoose.model('apiSchema', apiSchema);

export const taskManagerData = mongoose.model('taskSchema', taskSchema);
