import {
  MediaTypes,
  ParamTypes,
  StatusTypes,
  TaskManagerModelDTO,
  apiData,
  swaggerApiData,
  taskManagerData,
} from '../models/data-model';
import {QueryVars, TaskManagerReqDTO} from '../models/taskManager.model';
import {ApiService} from '../apiService/api.service';
import {
  ApiTask,
  ApiTaskDocServer,
  ApiTaskObj,
  DocServerCreatedTaskRes,
  FileProcessorStatus,
  performTaskObj,
} from './taskManger.dto';
import {getData, patchData, postData} from '../services/fetchReq';
import {
  getDataBackground,
  patchDataBackground,
  postDataBackground,
  putDataBackground
} from '../services/backgroundFetchReq';
import {
  CONNECTION_STR,
  DEFAULT_SYSTEM_ID,
  SEND_TO_MESSAGE_BUS,
  TOPIC_NAME,
  VECTOR_DB_INSERT_URL,
  AI_CLIENT_SERVER_STORE_URL,
  MESSAGE_BUS_SERVICE_NAME,
} from '../constants';
import {S2tServiceBusClient} from '../azureServiceBus/azureServiceBus.service';
import {quotaApiDeptId} from '../services/startupFunction';
import axios from 'axios';
import { TaskPriority } from '../utils/backgroundTaskProcessor';
import logger from '../logger';

export class TaskManagerService {
  apiService: ApiService;
  s2tServiceBusClient: S2tServiceBusClient;
  private static instance: TaskManagerService;

  constructor() {
    logger.info('[TaskManagerService] Initializing service');
    this.apiService = new ApiService();

    // Initialize the service bus client
    this.s2tServiceBusClient = new S2tServiceBusClient(
      CONNECTION_STR,
      DEFAULT_SYSTEM_ID,
      TOPIC_NAME,
      MESSAGE_BUS_SERVICE_NAME,
    );

    logger.info('[TaskManagerService] Service initialized successfully');

    // Store the instance for singleton pattern
    if (!TaskManagerService.instance) {
      TaskManagerService.instance = this;
    }

    // Setup cleanup on process exit
    process.on('exit', () => {
      this.dispose().catch(err => {
        logger.error(`[TaskManagerService] Error during cleanup on exit: ${err}`);
      });
    });

    // Handle other termination signals
    ['SIGINT', 'SIGTERM', 'SIGUSR2'].forEach(signal => {
      process.once(signal, () => {
        this.dispose().catch(err => {
          logger.error(`[TaskManagerService] Error during cleanup on ${signal}: ${err}`);
        }).finally(() => {
          process.exit(0);
        });
      });
    });
  }

  /**
   * Get the singleton instance of TaskManagerService
   */
  public static getInstance(): TaskManagerService {
    if (!TaskManagerService.instance) {
      TaskManagerService.instance = new TaskManagerService();
    }
    return TaskManagerService.instance;
  }

  /**
   * Properly dispose of resources
   */
  public async dispose(): Promise<void> {
    logger.info('[TaskManagerService] Disposing resources...');
    if (this.s2tServiceBusClient) {
      await this.s2tServiceBusClient.dispose();
    }
    logger.info('[TaskManagerService] Resources disposed successfully');
  }

  async getTask(taskId: string) {
    logger.info(`[TaskManagerService:getTask] Fetching task with ID: ${taskId}`);
    const task = await taskManagerData.findById(taskId);
    logger.debug(`[TaskManagerService:getTask] Task found: ${JSON.stringify(task)}`);
    return task;
  }

  replacer(key: any, value: any) {
    if (typeof value === 'number' && value % 1 === 0) {
      return value.toFixed(1); // This will convert 267.0 to "267.0"
    }
    return value;
  }

  async checkQuotaApiForDept() {
    const url = `${process.env.QUOTA_API_ENDPOINT_URL}/quota?id=${quotaApiDeptId}&assignType=dept&app=AI-Manager&action=TaskCreation`;
    logger.info(`[TaskManagerService:checkQuotaApiForDept] Checking quota at URL: ${url}`);
    try {
      await axios.post(url);
      logger.info('[TaskManagerService:checkQuotaApiForDept] Quota check successful');
      return true;
    } catch (error) {
      logger.info(`[TaskManagerService:checkQuotaApiForDept] Error response: ${error.response?.status}`);
      if (error.response?.status === 429) {
        logger.warn('[TaskManagerService:checkQuotaApiForDept] Quota limit reached');
        return false;
      }
      if (error.response?.status === 404) {
        logger.info('[TaskManagerService:checkQuotaApiForDept] No quota found, proceeding');
        return true;
      }
    }
    return true;
  }

  async getTaskData(taskId: string) {
    logger.info(`[TaskManagerService:getTaskData] Fetching task data for ID: ${taskId}`);
    const updatedObj = await taskManagerData.findById(taskId);
    logger.debug(`[TaskManagerService:getTaskData] Found task: ${JSON.stringify(updatedObj)}`);

    if (!updatedObj) {
      logger.warn('[TaskManagerService:getTaskData] No task found');
      throw new Error('No Data found');
    }

    const results = await this.getDocServerTaskResults(updatedObj.docServerId);
    logger.debug(`[TaskManagerService:getTaskData] Retrieved doc server results: ${JSON.stringify(results)}`);
    return results;
  }

  /**
   * Update a task with results and set its status to completed or error
   * @param taskId The ID of the task to update
   * @param results The results to update the task with
   * @returns Success message or the updated task
   */
  async updateTask(taskId: string, results: any) {
    logger.info(`[TaskManagerService:updateTask] Updating task ${taskId} with results: ${JSON.stringify(results)}`);
    let isError = false;
    if (results.message && results.message.includes('Error')) {
      logger.warn('[TaskManagerService:updateTask] Error detected in results');
      isError = true;
    }

    const newStatus = isError ? StatusTypes.ERROR : StatusTypes.COMPLETED;
    logger.info(`[TaskManagerService:updateTask] Setting new status: ${newStatus}`);

    const updatedObj = await taskManagerData.findByIdAndUpdate(taskId, {
      status: newStatus,
      completedDate: Date.now(),
    });

    if (updatedObj) {
      logger.debug(`[TaskManagerService:updateTask] Task updated: ${JSON.stringify(updatedObj)}`);
      const {fileId} = JSON.parse(updatedObj.apiBody);

      // Ensure docServerId exists
      if (!updatedObj.docServerId) {
        logger.warn('[TaskManagerService:updateTask] Warning: docServerId is undefined, creating a new one');

        try {
          // Create a new doc server task
          const callBackUrl = `${process.env.AI_GATEWAY_SERVICE_URL}/task/update?taskId=${taskId}`;

          const docServerTask = await this.createDocServerTask({
            apiUrl: updatedObj.apiUrl,
            apiTag: updatedObj.apiTag,
            apiBody: JSON.parse(updatedObj.apiBody),
            priority: updatedObj.priority,
            queryVars: updatedObj.queryVars,
            status: newStatus,
            taskId: taskId,
            callBackUrl,
            env: updatedObj.env || '',
            fileId: fileId || '',
            metaData: updatedObj.metaData || '',
          });

          // Update the task with the new docServerId
          await taskManagerData.findByIdAndUpdate(
            taskId,
            { docServerId: docServerTask._id }
          );

          // Update our local object
          updatedObj.docServerId = docServerTask._id;
          logger.info(`[TaskManagerService:updateTask] Created new docServerId: ${updatedObj.docServerId}`);
        } catch (error) {
          logger.error(`[TaskManagerService:updateTask] Error creating doc server task: ${error}`);
          throw new Error('Failed to create doc server task');
        }
      }

      // Now we can safely upload results to doc server
      logger.info('[TaskManagerService:updateTask] Uploading results to doc server');
      await this.uploadDocServerResultFiles(
        updatedObj.docServerId,
        results,
        fileId,
        updatedObj.serviceName,
        newStatus,
      );

      if (SEND_TO_MESSAGE_BUS) {
        logger.info('[TaskManagerService:updateTask] Sending message to bus');

        // We can safely use docServerId now since we've ensured it exists
        await this.s2tServiceBusClient.sendMessage({
          analysisCollectionId: updatedObj.docServerId,
          fileId,
          serviceName: updatedObj.serviceName,
          env: updatedObj.env,
          newStatus,
          taskId,
          description: 'Task Status Update',
          priority: updatedObj.priority,
        });
        await taskManagerData.updateOne(
          {_id: taskId},
          {_messageSentToBus: true},
        );
      } else {
        logger.info('[TaskManagerService:updateTask] Sending data to AI client server');
        await postData(AI_CLIENT_SERVER_STORE_URL, {
          fileId,
          status: newStatus,
          metaData: updatedObj.metaData,
          [updatedObj.serviceName]: results,
        });
        await taskManagerData.updateOne(
          {_id: taskId},
          {_messageSentClientServer: true},
        );
      }
    }

    logger.info('[TaskManagerService:updateTask] Update completed successfully');
    return 'Success';
  }

  /**
   * Create a new task and store it in the database
   * @param data The task data to create
   * @returns Array of created task responses
   */
  async createTask(data: TaskManagerReqDTO) {
    logger.info(`[TaskManagerService:createTask] Creating new task with data: ${JSON.stringify(data)}`);
    const body = JSON.parse(data.apiBody);
    const mediaType = data.mediaType;

    if (!mediaType) {
      logger.warn('[TaskManagerService:createTask] No media type provided');
      throw new Error('No MediaType Provided');
    }

    logger.info(`[TaskManagerService:createTask] Finding APIs for media type: ${mediaType}`);
    const apiWithSelectedMediaTypes = await apiData.find({mediaType});
    if (!apiWithSelectedMediaTypes) {
      logger.warn('[TaskManagerService:createTask] No APIs found for media type');
      return 'No API found with required Media Type';
    }

    const taskResponses = [];
    for (const api of apiWithSelectedMediaTypes) {
      try {
        logger.info(`[TaskManagerService:createTask] Processing API: ${api.serviceName}`);
        const {updatedData} = await this.paramsCheck(data, api);

        // Always set status to IN_QUEUE
        const taskStatus = StatusTypes.IN_QUEUE;
        // Prepare the task data
        const finalisedData = {
          ...updatedData,
          status: taskStatus,
          mediaType,
          entity_id: body.entity_id ?? '',
          crawler: body.crawler ?? '',
          env: data.env ?? '',
          fileId: body.fileId,
          // No processingStartDate since we're always setting to IN_QUEUE
        };

        logger.debug(`[TaskManagerService:createTask] Creating task with data: ${JSON.stringify(finalisedData)}`);
        const createdTask = await taskManagerData.create(finalisedData);

        const {status, _id: taskId} = createdTask;
        const callBackUrl = `${process.env.AI_GATEWAY_SERVICE_URL}/task/update?taskId=${taskId}`;

        logger.info('[TaskManagerService:createTask] Creating doc server task');
        const docServerTask = await this.createDocServerTask({
          ...finalisedData,
          status,
          taskId,
          callBackUrl,
          env: data.env ?? '',
          fileId: body.fileId,
          metaData: data.metaData ?? '',
        });

        logger.info('[TaskManagerService:createTask] Updating task with doc server ID');
        await taskManagerData.findByIdAndUpdate(
          createdTask._id,
          {docServerId: docServerTask._id, metaData: data.metaData ?? ''},
        );

        const taskResponse = {
          taskId,
          status: createdTask.status,
          apiUrl: updatedData.apiUrl,
        };

        logger.debug(`[TaskManagerService:createTask] Task response: ${JSON.stringify(taskResponse)}`);
        taskResponses.push(taskResponse);
      } catch (error) {
        logger.error(`[TaskManagerService:createTask] Error creating task: ${error}`);
        throw error;
      }
    }

    logger.info('[TaskManagerService:createTask] All tasks created successfully');
    return taskResponses;
  }

  appendQueryVariablesToUrl(baseUrl: string, queryVars: QueryVars[]) {
    const url = new URL(baseUrl);
    queryVars.forEach(queryVar => {
      url.searchParams.append(queryVar.variable, queryVar.value);
    });
    return url.href;
  }

  async paramsCheck(body: TaskManagerReqDTO, api: ApiTaskObj) {
    const apiObj = api;
    // if (apiObj?.requestType === "POST" && !body.apiBody){
    //   throw new Error("Body is requested for the intended POST API")
    // }
    if (apiObj?.queryParams.length !== body.queryVars.length) {
      throw new Error(
        'Query Variables passed in does not match with intended API',
      );
    }

    const apiUrl = this.appendQueryVariablesToUrl(
      apiObj.apiUrl,
      body.queryVars,
    );
    const serviceName = apiObj.serviceName;

    return {
      updatedData: {...body, apiUrl, serviceName, apiTag: apiObj.tag},
      requestType: apiObj.requestType,
    };
  }

  /**
   * Create a doc server task using background processing but wait for the result
   *
   * This is a hybrid approach:
   * - The HTTP request is made in the background via the task processor
   * - But this method waits for the result before returning (semi-blocking)
   *
   * We need to wait for the result because we need the docserver task ID
   * for the subsequent database update. Other docserver methods are fully
   * non-blocking because we don't need to wait for their results.
   *
   * @param data The task data to create
   * @returns Promise that resolves with the created task
   */
  async createDocServerTask(
    data: ApiTaskDocServer,
  ): Promise<DocServerCreatedTaskRes> {
    logger.info('[TaskManagerService:createDocServerTask] Creating doc server task in background');

    return new Promise((resolve, reject) => {
      // Use background processing for doc server task creation
      // but wait for the callback to resolve the promise
      postDataBackground(
        `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResults?database=Mongo&mergeArrayFields=true`,
        {...data, results: {}},
        TaskPriority.HIGH,
        (error, result) => {
          if (error) {
            logger.error(`[TaskManagerService:createDocServerTask] Error creating doc server task: ${error}`);
            reject(error);
          } else {
            logger.info(`[TaskManagerService:createDocServerTask] Doc server task created successfully: ${JSON.stringify(result)}`);
            resolve(result);
          }
        }
      );
    });
  }

  /**
   * Upload result files to doc server in the background
   * @param docServerId The doc server ID
   * @param data The data to upload
   * @param fileId The file ID
   * @param serviceName The service name
   * @param newStatus The new status
   */
  async uploadDocServerResultFiles(
    docServerId: string,
    data: any,
    fileId: string,
    serviceName: string,
    newStatus: StatusTypes,
  ) {
    logger.info('[TaskManagerService:uploadDocServerResultFiles] Uploading result files in background');
    logger.debug(`data: ${JSON.stringify(data)}, fileId: ${fileId}`);

    // Check if docServerId is defined
    if (!docServerId) {
      logger.error('[TaskManagerService:uploadDocServerResultFiles] Error: docServerId is undefined');
      return;
    }

    const tags = ['auto_analysis_images', 'facial_recognition'];
    const _fileProcessorStatus: FileProcessorStatus = {};
    _fileProcessorStatus[serviceName] = data.message ?? 'processed';

    // Use background processing for uploading result files
    patchDataBackground(
      `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResults/${docServerId}?mergeArrayFields=false`,
      {
        results: {...data, tags, _fileProcessorStatus},
        status: newStatus,
      },
      TaskPriority.MEDIUM,
      (error, _result) => {
        if (error) {
          logger.error(`[TaskManagerService:uploadDocServerResultFiles] Error uploading result files: ${error}`);
        } else {
          logger.info('[TaskManagerService:uploadDocServerResultFiles] Result files uploaded successfully');
        }
      }
    );
  }

  /**
   * Upload files with status to doc server in the background
   * @param data The data to upload
   * @param fileId The file ID
   */
  async uploadDocServerFilesWithStatus(data: any, fileId: string) {
    logger.info('[TaskManagerService:uploadDocServerFilesWithStatus] Uploading files with status in background');
    logger.debug(`data: ${JSON.stringify(data)}, fileId: ${fileId}`);

    // Use background processing for uploading files with status
    patchDataBackground(
      `${process.env.DOC_SERVER_BASE_URL}files/${fileId}?mergeArrayFields=true`,
      {...data},
      TaskPriority.MEDIUM,
      (error, _result) => {
        if (error) {
          logger.error(`[TaskManagerService:uploadDocServerFilesWithStatus] Error uploading files with status: ${error}`);
        } else {
          logger.info('[TaskManagerService:uploadDocServerFilesWithStatus] Files with status uploaded successfully');
        }
      }
    );
  }

  /**
   * Update doc server task status in the background
   * @param docServerId The doc server ID
   * @param newStatus The new status
   */
  async updateDocServerTaskStatus(docServerId: string, newStatus: StatusTypes) {
    logger.info(`[TaskManagerService:updateDocServerTaskStatus] Updating task status to ${newStatus} in background`);

    // Check if docServerId is defined
    if (!docServerId) {
      logger.error('[TaskManagerService:updateDocServerTaskStatus] Error: docServerId is undefined');
      return;
    }

    // Use background processing for updating task status
    patchDataBackground(
      `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResults/${docServerId}?mergeArrayFields=false`,
      {status: newStatus},
      TaskPriority.HIGH,
      (error, _result) => {
        if (error) {
          logger.error(`[TaskManagerService:updateDocServerTaskStatus] Error updating task status: ${error}`);
        } else {
          logger.info('[TaskManagerService:updateDocServerTaskStatus] Task status updated successfully');
        }
      }
    );
  }

  /**
   * Update doc server task results in the background
   * @param docServerId The doc server ID
   * @param data The data to update
   * @param newStatus The new status
   */
  async updateDocServerTaskResults(
    docServerId: string,
    data: any,
    newStatus: StatusTypes,
  ) {
    logger.info('[TaskManagerService:updateDocServerTaskResults] Updating task results in background');

    // Check if docServerId is defined
    if (!docServerId) {
      logger.error('[TaskManagerService:updateDocServerTaskResults] Error: docServerId is undefined');
      return;
    }

    // Use background processing for updating task results
    patchDataBackground(
      `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResults/${docServerId}?mergeArrayFields=false`,
      {results: data, status: newStatus},
      TaskPriority.MEDIUM,
      (error, _result) => {
        if (error) {
          logger.error(`[TaskManagerService:updateDocServerTaskResults] Error updating task results: ${error}`);
        } else {
          logger.info('[TaskManagerService:updateDocServerTaskResults] Task results updated successfully');
        }
      }
    );
  }

  /**
   * Get doc server task results
   * @param docServerId The doc server ID
   * @returns The task results
   */
  async getDocServerTaskResults(docServerId: string) {
    logger.info('[TaskManagerService:getDocServerTaskResults] Getting task results');

    // Check if docServerId is defined
    if (!docServerId) {
      logger.error('[TaskManagerService:getDocServerTaskResults] Error: docServerId is undefined');
      // Return empty results instead of error
      return { results: {}, status: StatusTypes.IN_QUEUE };
    }

    // For getting results, we need to use the synchronous version to get the actual data
    return await getData(
      `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResults/${docServerId}`,
    );
  }

  /**
   * Get media type from doc server
   * @param docServerId The doc server ID
   * @returns The media type
   */
  async getMediaType(docServerId: string) {
    // Check if docServerId is defined
    if (!docServerId) {
      logger.error('[TaskManagerService:getMediaType] Error: docServerId is undefined');
      // Default to IMAGE type if docServerId is undefined
      logger.warn('[TaskManagerService:getMediaType] Defaulting to IMAGE media type');
      return MediaTypes.IMAGE;
    }

    try {
      const {fileType} = await getData(
        `${process.env.DOC_SERVER_BASE_URL}files/${docServerId}?download=false`,
      );
      if (fileType === 'image') return MediaTypes.IMAGE;
      else if (fileType === 'video') return MediaTypes.VIDEO;
      else if (fileType === 'text') return MediaTypes.TEXT;
      else if (fileType === 'audio') return MediaTypes.AUDIO;
      else {
        logger.warn(`[TaskManagerService:getMediaType] Unrecognized file type: ${fileType}, defaulting to IMAGE`);
        return MediaTypes.IMAGE;
      }
    } catch (error) {
      logger.error(`[TaskManagerService:getMediaType] Error getting media type: ${error}`);
      // Default to IMAGE type if there's an error
      return MediaTypes.IMAGE;
    }
  }

  /**
   * Perform a task by updating its status and making the appropriate API call
   * @param taskObj The task object to perform
   */
  async perFormTask(taskObj: performTaskObj) {
    logger.info(`[TaskManagerService:perFormTask] Performing task: ${taskObj._id}`);
    const api = await apiData.findOne({tag: taskObj.apiTag});
    if (api) {
      try {
        const futureStatus = StatusTypes.IN_PROGRESS;
        // Update Docserver status to in progress
        if (taskObj.status !== futureStatus) {
          logger.info('[TaskManagerService:perFormTask] Updating task status to IN_PROGRESS');

          // Ensure docServerId exists
          if (!taskObj.docServerId) {
            logger.warn('[TaskManagerService:perFormTask] Warning: docServerId is undefined, creating a new one');

            // Get the task details from the database to ensure we have all fields
            const taskDetails = await taskManagerData.findById(taskObj._id);
            if (!taskDetails) {
              throw new Error(`Task with ID ${taskObj._id} not found in database`);
            }

            // Create a new doc server task
            const callBackUrl = `${process.env.AI_GATEWAY_SERVICE_URL}/task/update?taskId=${taskObj._id}`;
            const apiBody = JSON.parse(taskDetails.apiBody);

            try {
              const docServerTask = await this.createDocServerTask({
                apiUrl: taskDetails.apiUrl,
                apiTag: taskDetails.apiTag,
                apiBody: JSON.parse(taskDetails.apiBody),
                priority: taskDetails.priority,
                queryVars: taskDetails.queryVars,
                status: futureStatus,
                taskId: taskObj._id.toString(),
                callBackUrl,
                env: taskDetails.env || '',
                fileId: apiBody.fileId || '',
                metaData: taskDetails.metaData || '',
              });

              // Update the task with the new docServerId
              await taskManagerData.findByIdAndUpdate(
                taskObj._id,
                { docServerId: docServerTask._id }
              );

              // Update our local object
              taskObj.docServerId = docServerTask._id;
              logger.info(`[TaskManagerService:perFormTask] Created new docServerId: ${taskObj.docServerId}`);
            } catch (error) {
              logger.error(`[TaskManagerService:perFormTask] Error creating doc server task: ${error}`);
              throw new Error('Failed to create doc server task');
            }
          }

          // Now we can safely update the doc server status
          await this.updateDocServerTaskStatus(
            taskObj.docServerId,
            futureStatus,
          );

          // First, check if the task already has a processingStartDate
          const existingTask = await taskManagerData.findById(taskObj._id);

          // Update MongoDB server with status and set processingStartDate if not already set
          const updateData: any = {
            status: futureStatus
          };

          // Only set processingStartDate if it's not already set
          if (!existingTask?.processingStartDate) {
            updateData.processingStartDate = new Date();
          }

          await taskManagerData.findByIdAndUpdate(taskObj._id, updateData);
        }

        // Prepare API call
        logger.info(
          `[TaskManagerService:perFormTask] Calling API: ${taskObj.apiUrl} with method ${api.requestType}`,
        );
        const callbackUrl = `${process.env.AI_GATEWAY_SERVICE_URL}/task/update?taskId=${taskObj._id}`;

        let apiBody = JSON.parse(taskObj.apiBody);
        apiBody.callbackUrl = callbackUrl;

        // Handle special cases for different services
        if (api.serviceName === 'FacialRecognition') {
          if (apiBody.entity_id !== '') apiBody.entity_id = taskObj.entity_id;
          if (apiBody.crawler !== '') apiBody.crawler = taskObj.crawler;
        }

        if (api.serviceName === 'ObjectDetection') {
          const {redaction, ...newApiBody} = apiBody;
          apiBody = newApiBody;
        }

        logger.debug(`[TaskManagerService:perFormTask] API body: ${JSON.stringify(apiBody)}`);

        // Perform the API call
        await this.performApiCall(
          api.requestType,
          taskObj.apiUrl,
          apiBody
        );
      } catch (error) {
        logger.error(`[TaskManagerService:perFormTask] Error performing task: ${error}`);
        throw error;
      }
    } else {
      logger.error(`[TaskManagerService:perFormTask] No API found for tag: ${taskObj.apiTag}`);
    }
  }

  /**
   * Process the next highest priority task for a given service
   * @param serviceName The service name to process tasks for
   * @returns The processed task or null if no task was found
   */
  async processNextTask(serviceName: string): Promise<any> {
    logger.info(`[TaskManagerService:processNextTask] Finding next task for service: ${serviceName}`);

    const [latestHighPriorityTask] = await taskManagerData
      .find({serviceName, status: StatusTypes.IN_QUEUE})
      .sort({priority: -1, _id: 1})
      .limit(1);

    if (latestHighPriorityTask) {
      logger.info(`[TaskManagerService:processNextTask] Found high priority task: ${JSON.stringify(latestHighPriorityTask)}`);

      // The processingStartDate will be set in the perFormTask method
      await this.perFormTask(latestHighPriorityTask);
      return latestHighPriorityTask;
    }

    logger.info('[TaskManagerService:processNextTask] No tasks found in queue');
    return null;
  }

  /**
   * Perform an API call based on the request type
   * @param requestType The HTTP method to use (GET, POST, PATCH)
   * @param apiUrl The URL to call
   * @param apiBody The body to send with the request
   */
  async performApiCall(
    requestType: string,
    apiUrl: string,
    apiBody: string,
  ) {
    logger.info(`[TaskManagerService:performApiCall] Making ${requestType} request to ${apiUrl}`);

    // Check if this is a docserver call
    const isDocServerCall = apiUrl.includes(process.env.DOC_SERVER_BASE_URL || '');

    if (isDocServerCall) {
      logger.info('[TaskManagerService:performApiCall] Using background processing for docserver call');

      // Use background processing for docserver calls
      if (requestType === ParamTypes.GET) {
        getDataBackground(apiUrl, TaskPriority.HIGH);
      } else if (requestType === ParamTypes.POST) {
        postDataBackground(apiUrl, apiBody, TaskPriority.HIGH);
      } else if (requestType === ParamTypes.PATCH) {
        patchDataBackground(apiUrl, apiBody, TaskPriority.HIGH);
      }
    } else {
      // Use synchronous processing for non-docserver calls
      if (requestType === ParamTypes.GET) {
        getData(apiUrl);
      } else if (requestType === ParamTypes.POST) {
        postData(apiUrl, apiBody);
      } else if (requestType === ParamTypes.PATCH) {
        patchData(apiUrl, apiBody);
      }
    }

    return;
  }
}
