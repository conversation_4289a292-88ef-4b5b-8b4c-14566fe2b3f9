import { EventEmitter } from 'events';
import logger from '../logger';

/**
 * Task priority levels
 */
export enum TaskPriority {
  HIGH = 3,
  MEDIUM = 2,
  LOW = 1
}

/**
 * Interface for background tasks
 */
export interface BackgroundTask {
  id: string;
  type: string;
  priority: TaskPriority;
  data: any;
  retries?: number;
  maxRetries?: number;
  createdAt: Date;
}

/**
 * Background task processor for handling non-blocking operations
 */
export class BackgroundTaskProcessor {
  private static instance: BackgroundTaskProcessor;
  private taskQueue: BackgroundTask[] = [];
  private processing: boolean = false;
  private eventEmitter: EventEmitter = new EventEmitter();
  private maxConcurrentTasks: number = 5;
  private activeTasks: number = 0;
  private taskHandlers: Map<string, (task: BackgroundTask) => Promise<void>> = new Map();

  private constructor() {
    // Set max listeners to avoid memory leak warnings
    this.eventEmitter.setMaxListeners(100);

    // Listen for task completion
    this.eventEmitter.on('taskComplete', () => {
      this.activeTasks--;
      this.processNextTasks();
    });

    // Listen for task failure
    this.eventEmitter.on('taskFailed', (task: BackgroundTask) => {
      this.activeTasks--;

      // Retry logic
      if (task.retries && task.maxRetries && task.retries < task.maxRetries) {
        logger.info(`[BackgroundTaskProcessor] Retrying task ${task.id} (${task.retries + 1}/${task.maxRetries})`);
        task.retries++;
        this.taskQueue.push(task);
      } else {
        logger.error(`[BackgroundTaskProcessor] Task ${task.id} failed after ${task.retries} retries`);
      }

      this.processNextTasks();
    });
  }

  /**
   * Get the singleton instance of BackgroundTaskProcessor
   */
  public static getInstance(): BackgroundTaskProcessor {
    if (!BackgroundTaskProcessor.instance) {
      BackgroundTaskProcessor.instance = new BackgroundTaskProcessor();
    }
    return BackgroundTaskProcessor.instance;
  }

  /**
   * Register a handler for a specific task type
   * @param taskType The type of task to handle
   * @param handler The handler function
   */
  public registerTaskHandler(taskType: string, handler: (task: BackgroundTask) => Promise<void>): void {
    this.taskHandlers.set(taskType, handler);
    logger.info(`[BackgroundTaskProcessor] Registered handler for task type: ${taskType}`);
  }

  /**
   * Add a task to the queue
   * @param task The task to add
   */
  public enqueueTask(task: Omit<BackgroundTask, 'id' | 'createdAt'>): string {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    const fullTask: BackgroundTask = {
      ...task,
      id: taskId,
      createdAt: new Date(),
      retries: 0,
      maxRetries: task.maxRetries || 3
    };

    this.taskQueue.push(fullTask);
    logger.info(`[BackgroundTaskProcessor] Task ${taskId} added to queue (type: ${task.type})`);

    // Start processing if not already processing
    if (!this.processing) {
      this.processNextTasks();
    }

    return taskId;
  }

  /**
   * Process the next tasks in the queue
   */
  private processNextTasks(): void {
    if (this.taskQueue.length === 0) {
      this.processing = false;
      return;
    }

    this.processing = true;

    // Process tasks up to the maximum concurrent limit
    while (this.taskQueue.length > 0 && this.activeTasks < this.maxConcurrentTasks) {
      // Sort tasks by priority (higher number = higher priority)
      this.taskQueue.sort((a, b) => b.priority - a.priority);

      const task = this.taskQueue.shift();
      if (!task) continue;

      this.activeTasks++;
      this.processTask(task);
    }
  }

  /**
   * Process a single task
   * @param task The task to process
   */
  private async processTask(task: BackgroundTask): Promise<void> {
    logger.info(`[BackgroundTaskProcessor] Processing task ${task.id} (type: ${task.type})`);

    try {
      const handler = this.taskHandlers.get(task.type);
      if (!handler) {
        throw new Error(`No handler registered for task type: ${task.type}`);
      }

      await handler(task);
      logger.info(`[BackgroundTaskProcessor] Task ${task.id} completed successfully`);
      this.eventEmitter.emit('taskComplete');
    } catch (error) {
      logger.error(`[BackgroundTaskProcessor] Error processing task ${task.id}: ${error}`);
      this.eventEmitter.emit('taskFailed', task);
    }
  }

  /**
   * Get the current queue length
   */
  public getQueueLength(): number {
    return this.taskQueue.length;
  }

  /**
   * Get the number of active tasks
   */
  public getActiveTasksCount(): number {
    return this.activeTasks;
  }
}
