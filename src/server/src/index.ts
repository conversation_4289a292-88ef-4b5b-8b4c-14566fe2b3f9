import mongoose from 'mongoose';
import {ApplicationConfig, ExpressServer} from './server';
import dotenv from 'dotenv';
import 'amqplib';
import {checkAndRetrieveTasks} from './services/startupService';
import {scheduledFunctions} from './services/cronJobService';
import {startupFunction} from './services/startupFunction';

export * from './server';

dotenv.config();

export async function main(options: ApplicationConfig = {}) {
  const mongooseUrl = `${process.env.MONGO_CONNECTION_STRING}/Consolidated_AI_Models_API_DB?authSource=admin`;
  mongoose.connect(mongooseUrl);
  const con = mongoose.connection;
  const app = new ExpressServer(options);
  await app.boot();
  await app.start();
  await startupFunction();
  scheduledFunctions();

  const url = `http://${options.rest.host}:${options.rest.port}`;

  try {
    con.on('open', () => {
      console.log('connected');
    });
  } catch (error) {
    console.log('Error: ' + error);
  }

  console.log(`Server is running at ${url}`);
  console.log(`Try ${url}/`);
  console.log(`Try ${url}/api/ping`);

  await checkAndRetrieveTasks();

  return app;
}

if (require.main === module) {
  // Run the application
  const config = {
    rest: {
      port: +(process.env.PORT ?? 3000),
      host: process.env.HOST ?? 'localhost',
      // The `gracePeriodForClose` provides a graceful close for http/https
      // servers with keep-alive clients. The default value is `Infinity`
      // (don't force-close). If you want to immediately destroy all sockets
      // upon stop, set its value to `0`.
      // See https://www.npmjs.com/package/stoppable
      gracePeriodForClose: 5000, // 5 seconds
      openApiSpec: {
        // useful when used with OpenAPI-to-GraphQL to locate your application
        setServersFromRequest: true,
      },
      // This is to mount LB4 as route in Express
      listenOnStart: false,
    },
  };
  main(config).catch(err => {
    console.error('Cannot start the application.', err);
    process.exit(1);
  });
}
