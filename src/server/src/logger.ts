import { createLogger, format, transports } from 'winston';
import path from 'path';

const { combine, timestamp, printf } = format;

const lineInfo = format((info) => {
  const oldPrepareStackTrace = Error.prepareStackTrace;
  Error.prepareStackTrace = (_, stack) => stack;
  const err = new Error();
  const stack = err.stack as unknown as NodeJS.CallSite[];
  Error.prepareStackTrace = oldPrepareStackTrace;

  const caller = stack[10]; // may need adjustment depending on call depth
  if (caller) {
    const file = caller.getFileName();
    const line = caller.getLineNumber();
    info.line = `${path.relative(process.cwd(), file || '')}:${line}`;
  } else {
    info.line = 'unknown';
  }

  return info;
});

const logFormat = printf(({ timestamp, level, message, line }) => {
  return `${timestamp} [${level}] (${line}) ${message}`;
});

const logger = createLogger({
  level: 'info',
  format: combine(
    lineInfo(),
    timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    logFormat
  ),
  transports: [new transports.Console()],
});

export default logger;
