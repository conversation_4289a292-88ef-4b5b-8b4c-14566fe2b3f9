import {getData, postData, putData} from './fetchReq';
import { BackgroundTaskProcessor } from '../utils/backgroundTaskProcessor';
import logger from '../logger';

export const quotaApiDeptId = `AI_Manager_${process.env.SYSTEM_ID}`;
// export const quotaApiDeptId = `dept2`;

export const checkAndCreateApiDept = async () => {
  const isDeptAvail = await getData(
    `${process.env.QUOTA_API_ENDPOINT_URL}/quota/user?id=${quotaApiDeptId}`,
  );
  console.log(isDeptAvail);
  if (!isDeptAvail.length) {
    console.log(`dept ${quotaApiDeptId} not found`);
    const deptCreationInfo = {
      app: 'AI-Manager',
      action: 'TaskCreation',
      assignType: 'dept',
      assignTo: `${quotaApiDeptId}`,
      limit: 20000,
      period: 'monthly',
    };

    await postData(
      `${process.env.QUOTA_API_ENDPOINT_URL}/quota/setting`,
      deptCreationInfo,
    );
  }
};

/**
 * Initialize the background task processor
 */
export const initializeBackgroundTaskProcessor = () => {
  // Get the singleton instance to initialize it
  const backgroundTaskProcessor = BackgroundTaskProcessor.getInstance();
  console.log('[startupFunction] Background task processor initialized');
};

export const startupFunction = async () => {
  // Initialize the background task processor
  initializeBackgroundTaskProcessor();

  const analysisCollection = await getData(
    `${process.env.DOC_SERVER_BASE_URL}docs/AnalysisResul?itemsPerPage=10&pageNumber=1`,
  );

  if (!analysisCollection) {
    logger.error('[startupFunction] Error getting analysis collection');
  }

  if (process.env.SYSTEM_ID === 'cybersmart-dev-akscluster2') {
    try {
      // await checkAndCreateApiDept();
    } catch (error) {
      throw new Error(`Cannot Check/Create Quota Api dept: ${error}`);
    }
  }
};
