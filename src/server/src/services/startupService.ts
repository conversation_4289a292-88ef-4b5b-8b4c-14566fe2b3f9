import {ApiService} from '../apiService/api.service';
import {IMAGE_KT, VIDEO_KT} from '../constants';
import {StatusTypes, taskManagerData} from '../models/data-model';
import {TaskManagerService} from '../taskManager/taskManager.service';
import logger from '../logger';
// Use the singleton instance of TaskManagerService
const taskManagerService = TaskManagerService.getInstance();

export async function checkAndRetrieveTasks() {

  try {
    const apiService = new ApiService();
    const serviceNames = await apiService.getAllServiceNames();
    logger.info(`Available service names: ${JSON.stringify(serviceNames)}`);
    for (const serviceName of serviceNames) {
      // Find all tasks in 'processing' state
      const processingTasks = await taskManagerData.find({
        serviceName,
        status: StatusTypes.IN_PROGRESS,
      });

      for (const processingTask of processingTasks) {
        logger.debug(`Processing task: ${JSON.stringify(processingTask)}`);
        const checkProcessingTaskTime =
          processingTask.mediaType === 'Video' ? VIDEO_KT : IMAGE_KT;
        // Determine which date to use for timeout calculation
        const dateToUse = processingTask.processingStartDate || processingTask.createdDate;
        const dateType = processingTask.processingStartDate ? 'processing' : 'creation';

        if (
          !processingTask.completedDate &&
          dateToUse &&
          dateToUse.getTime() < Date.now() - checkProcessingTaskTime
        ) {
          const timeElapsed = Date.now() - dateToUse.getTime();
          const timeElapsedSeconds = Math.floor(timeElapsed / 1000);
          const timeElapsedMinutes = Math.floor(timeElapsedSeconds / 60);

          logger.info(`Setting task with id: ${processingTask.id} to error - ${dateType} time exceeded. Time passed: ${timeElapsedMinutes} minutes (${timeElapsedSeconds} seconds)`);
          await taskManagerService.updateTask(processingTask.id, {
            message: `Error Processing Task - ${dateType.charAt(0).toUpperCase() + dateType.slice(1)} Time Exceeded`,
            status: StatusTypes.IN_QUEUE,
          });
        }
      }

      // IMPORTANT FIX: Re-check if there are still any tasks in progress after handling timeouts
      const remainingProcessingTasksCount = await taskManagerData.countDocuments({
        serviceName,
        status: StatusTypes.IN_PROGRESS,
      });

      // Only proceed if there are no tasks in progress
      if (remainingProcessingTasksCount === 0) {
        // Retrieve the latest task with the highest priority
        const [latestHighPriorityTask] = await taskManagerData
          .find({serviceName, status: StatusTypes.IN_QUEUE})
          .sort({priority: -1, _id: 1}) // Assuming _id is used for creation time
          .limit(1);

        if (latestHighPriorityTask) {
          logger.info(
            `Startup Function: Service: ${serviceName}, Task: ${JSON.stringify(latestHighPriorityTask)}`
          );
          taskManagerService.perFormTask(latestHighPriorityTask);
        }
      } else {
        logger.info(`Skipping new task execution for service ${serviceName} as there are still ${remainingProcessingTasksCount} tasks in progress`);
      }
    }
  } catch (error) {
    logger.error(`Error in checkAndRetrieveTasks: ${error}`);
  }
}
